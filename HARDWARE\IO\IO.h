#ifndef __IO_
#define __IO_


#define E7  PEout(7)
#define E9  PEout(9)
#define E11  PEout(11)
#define E13  PEout(13)

// PF13和PF14控制引脚宏定义
#define PF13_CTRL  PFout(13)
#define PF14_CTRL  PFout(14)

// 控制函数宏定义
#define PF13_HIGH()  (PF13_CTRL = 1)
#define PF13_LOW()   (PF13_CTRL = 0)
#define PF14_HIGH()  (PF14_CTRL = 1)
#define PF14_LOW()   (PF14_CTRL = 0)

void IO_Init(void);//初始化

// PF13和PF14控制函数
void PF13_Set_High(void);   // 设置PF13为高电平
void PF13_Set_Low(void);    // 设置PF13为低电平
void PF14_Set_High(void);   // 设置PF14为高电平
void PF14_Set_Low(void);    // 设置PF14为低电平
void PF13_PF14_Test(void);  // PF13和PF14测试函数

#endif
