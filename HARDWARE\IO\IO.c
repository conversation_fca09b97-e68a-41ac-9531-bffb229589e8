#include "IO.h"
#include "sys.h"
#include "delay.h"

void IO_Init(void)
{

GPIO_InitTypeDef  GPIO_InitStructure;

// 使能GPIOE时钟
RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOE, ENABLE);

// 使能GPIOF时钟
RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOF, ENABLE);

// 初始化GPIOE引脚
GPIO_InitStructure.GPIO_Pin = GPIO_Pin_13 | GPIO_Pin_11| GPIO_Pin_9| GPIO_Pin_7;
GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;//普通输出模式
GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;//推挽输出
GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;//50MHz
GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_DOWN;//下拉
GPIO_Init(GPIOE, &GPIO_InitStructure);//初始化

GPIO_SetBits(GPIOE,GPIO_Pin_13 | GPIO_Pin_11| GPIO_Pin_9| GPIO_Pin_7);

// 初始化PF13和PF14引脚
GPIO_InitStructure.GPIO_Pin = GPIO_Pin_13 | GPIO_Pin_14;
GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;//普通输出模式
GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;//推挽输出
GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;//50MHz
GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_DOWN;//下拉
GPIO_Init(GPIOF, &GPIO_InitStructure);//初始化

// 初始状态设置为高电平
GPIO_SetBits(GPIOF, GPIO_Pin_13 | GPIO_Pin_14);
}

// PF13控制函数
void PF13_Set_High(void)
{
    PF13_HIGH();
}

void PF13_Set_Low(void)
{
    PF13_LOW();
}

// PF14控制函数
void PF14_Set_High(void)
{
    PF14_HIGH();
}

void PF14_Set_Low(void)
{
    PF14_LOW();
}

// PF13和PF14测试函数
void PF13_PF14_Test(void)
{
    // 测试PF13
    PF13_Set_High();
    delay_ms(500);
    PF13_Set_Low();
    delay_ms(500);

    // 测试PF14
    PF14_Set_High();
    delay_ms(500);
    PF14_Set_Low();
    delay_ms(500);

    // 同时测试
    PF13_Set_High();
    PF14_Set_High();
    delay_ms(500);
    PF13_Set_Low();
    PF14_Set_Low();
    delay_ms(500);
}

// 按键控制函数实现
static uint8_t pf13_pf14_current_state = 1;  // 当前状态，1=高电平，0=低电平

// 设置PF13和PF14都为高电平
void PF13_PF14_Set_Both_High(void)
{
    PF13_Set_High();
    PF14_Set_High();
    pf13_pf14_current_state = 1;
}

// 设置PF13和PF14都为低电平
void PF13_PF14_Set_Both_Low(void)
{
    PF13_Set_Low();
    PF14_Set_Low();
    pf13_pf14_current_state = 0;
}

// 切换PF13和PF14的状态（用于第5个按钮）
void PF13_PF14_Toggle_State(void)
{
    if (pf13_pf14_current_state == 1) {
        PF13_PF14_Set_Both_Low();
    } else {
        PF13_PF14_Set_Both_High();
    }
}

// 获取当前状态（0=低电平，1=高电平）
uint8_t PF13_PF14_Get_State(void)
{
    return pf13_pf14_current_state;
}

